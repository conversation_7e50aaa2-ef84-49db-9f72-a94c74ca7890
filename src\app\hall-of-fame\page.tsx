import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Book<PERSON><PERSON>,
  Crown,
  TrendingUp,
  Users,
  Medal,
  Star,
  Award
} from "lucide-react";
import { createClient } from "../../../supabase/server";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { getRankingLeaderboard } from "@/utils/ranking-system";
import RankDisplay, { LeaderboardRank } from "@/components/rank-display";
import StyledAvatar from "@/components/styled-avatar";
import PlayerStatsPopup from "@/components/player-stats-popup";

export default async function LeaderboardPage() {
  const supabase = await createClient();

  // Get ranking leaderboard data
  const globalRankingLeaderboard = await getRankingLeaderboard(50);

  // Group players by rank tiers for display
  const playersByTier = {
    Luminari: globalRankingLeaderboard.filter(p => p.rankInfo.tier === 'Luminari'),
    Sage: globalRankingLeaderboard.filter(p => p.rankInfo.tier === 'Sage'),
    Lexicon: globalRankingLeaderboard.filter(p => p.rankInfo.tier === 'Lexicon'),
    Quill: globalRankingLeaderboard.filter(p => p.rankInfo.tier === 'Quill'),
    Parchment: globalRankingLeaderboard.filter(p => p.rankInfo.tier === 'Parchment'),
  };

  // Get overall stats
  const { data: totalPlayersData } = await supabase
    .from('players')
    .select('id', { count: 'exact' })
    .gt('rankpoints', 0);

  const totalRankedPlayers = totalPlayersData?.length || 0;

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Crown className="h-8 w-8 text-amber-500" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent">
                The Scribe's Ascent
              </h1>
              <Crown className="h-8 w-8 text-amber-500" />
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Climb the ladder and solidify your place as a true master of words. Rankings are based on Rank Points (RP) earned through competitive matches.
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>{totalRankedPlayers} Ranked Players</span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                <span>Live Rankings</span>
              </div>
            </div>
          </header>

          {/* Top Ranked Players */}
          {globalRankingLeaderboard.length > 0 && (
            <section className="bg-gradient-to-r from-amber-100 via-white to-amber-100 rounded-2xl p-6 border-2 border-amber-200 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <Crown className="h-6 w-6 text-amber-600" />
                <h2 className="text-2xl font-bold text-amber-900">Elite Scribes</h2>
                <Badge variant="secondary" className="bg-amber-200 text-amber-800">
                  Top {Math.min(globalRankingLeaderboard.length, 10)} Players
                </Badge>
              </div>

              <div className="space-y-3">
                {globalRankingLeaderboard.slice(0, 10).map((player, index) => (
                  <LeaderboardRank
                    key={player.id}
                    rp={player.rankpoints}
                    position={index + 1}
                    playerName={player.display_name}
                    avatar={player.avatar_url}
                    className="bg-white/50 hover:bg-white/80 transition-colors"
                  />
                ))}
              </div>
            </section>
          )}

          {/* Rank Tier Leaderboards */}
          <section className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Rank Tiers</h2>
              <p className="text-gray-600">Players organized by their current rank in The Scribe's Ascent</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {Object.entries(playersByTier).reverse().map(([tierName, players]) => {
                if (players.length === 0) return null;

                const tierInfo = {
                  Luminari: { icon: Crown, color: "text-yellow-500", bgColor: "bg-yellow-100", borderColor: "border-yellow-200", gradientFrom: "from-yellow-50", gradientTo: "to-yellow-100" },
                  Sage: { icon: Award, color: "text-purple-500", bgColor: "bg-purple-100", borderColor: "border-purple-200", gradientFrom: "from-purple-50", gradientTo: "to-purple-100" },
                  Lexicon: { icon: BookOpen, color: "text-blue-500", bgColor: "bg-blue-100", borderColor: "border-blue-200", gradientFrom: "from-blue-50", gradientTo: "to-blue-100" },
                  Quill: { icon: Star, color: "text-green-500", bgColor: "bg-green-100", borderColor: "border-green-200", gradientFrom: "from-green-50", gradientTo: "to-green-100" },
                  Parchment: { icon: Medal, color: "text-gray-500", bgColor: "bg-gray-100", borderColor: "border-gray-200", gradientFrom: "from-gray-50", gradientTo: "to-gray-100" }
                };

                const tier = tierInfo[tierName as keyof typeof tierInfo];
                if (!tier) return null;

                return (
                  <Card
                    key={`tier-${tierName}`}
                    className={cn(
                      "overflow-hidden border-2 shadow-lg transition-all duration-300 hover:shadow-xl",
                      tier.borderColor
                    )}
                  >
                    {/* Header */}
                    <CardHeader className={cn(
                      "bg-gradient-to-r p-6",
                      tier.gradientFrom,
                      tier.gradientTo
                    )}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <tier.icon className={cn("h-6 w-6", tier.color)} />
                          <CardTitle className="text-xl">{tierName}</CardTitle>
                        </div>
                        <Badge variant="secondary" className="bg-white/80">
                          {players.length} Players
                        </Badge>
                      </div>

                      {/* Tier Stats */}
                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{players[0]?.rankpoints.toLocaleString() || 0}</p>
                          <p className="text-xs text-gray-600">Highest RP</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{players[players.length - 1]?.rankpoints.toLocaleString() || 0}</p>
                          <p className="text-xs text-gray-600">Entry RP</p>
                        </div>
                      </div>
                    </CardHeader>

                    {/* Tier Leaderboard */}
                    <CardContent className="p-0">
                      <div className="divide-y divide-gray-100 max-h-96 overflow-y-auto">
                        {players.slice(0, 10).map((player, index) => (
                          <div
                            key={`${tierName}-${player.id}`}
                            className="flex items-center justify-between p-4 hover:bg-gray-50/50 transition-colors"
                          >
                            <div className="flex items-center gap-4">
                              <div className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                                index === 0 ? "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white" :
                                index === 1 ? "bg-gradient-to-r from-gray-300 to-gray-400 text-white" :
                                index === 2 ? "bg-gradient-to-r from-amber-600 to-amber-700 text-white" :
                                "bg-gray-100 text-gray-700"
                              )}>
                                {index + 1}
                              </div>
                              <PlayerStatsPopup
                                playerId={player.id}
                                playerName={player.display_name}
                                playerAvatar={player.avatar_url}
                                playerBorder={player.avatar_border}
                              >
                                <div className="flex items-center gap-4 flex-1 min-w-0 cursor-pointer hover:bg-gray-50 rounded p-1 transition-colors">
                                  <StyledAvatar
                                    src={player.avatar_url}
                                    alt={player.display_name}
                                    fallback={player.display_name.substring(0, 2).toUpperCase()}
                                    size="sm"
                                    border={player.avatar_border}
                                  />
                                  <div className="flex-1 min-w-0">
                                    <p className="font-semibold text-gray-900 truncate">{player.display_name}</p>
                                    <RankDisplay rp={player.rankpoints} variant="inline" showRP={false} />
                                  </div>
                                </div>
                              </PlayerStatsPopup>
                            </div>

                            <div className="text-right">
                              <p className="font-bold text-lg text-amber-700">
                                {player.rankpoints.toLocaleString()} RP
                              </p>
                              <p className="text-sm text-gray-600">
                                Peak: {player.highest_rankpoints.toLocaleString()} RP
                              </p>
                            </div>
                          </div>
                        ))}
                        {players.length > 10 && (
                          <div className="p-4 text-center text-gray-500">
                            <p className="text-sm">... and {players.length - 10} more players</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
