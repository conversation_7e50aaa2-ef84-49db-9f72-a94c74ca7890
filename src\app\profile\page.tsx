import React from 'react';
import { createClient } from '../../../supabase/server';
import { redirect } from 'next/navigation';
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Trophy,
  History,
  Flame,
  Award,
  Settings,
  Calendar,
  BarChart4,
  Star,
  Volume2,
  VolumeX,
  Edit,
  Crown,
  Target,
  TrendingUp,
  Users,
  Clock,
  Medal,
  Zap,
  Shield,
  Lock,
  Check,
  BookOpen
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { getUserMatchHistory, getPlayerStats, formatMatchDuration, getRankDisplay, getDifficultyColor } from "@/utils/profile-utils";
import AvatarSelector from "@/components/avatar-selector";
import ProfileEditPopup from "@/components/profile-edit-popup";
import BorderSelector from "@/components/border-selector";
import RankDisplay from "@/components/rank-display";
import PrivacySettings from "@/components/privacy-settings";
import { getRankInfo } from "@/utils/ranking-system";

export default async function ProfilePage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  // Get user's player data
  const { data: playerData } = await supabase
    .from('players')
    .select('*')
    .eq('id', user.id)
    .single();

  // Get real data
  const [matchHistory, playerStats] = await Promise.all([
    getUserMatchHistory(user.id, 20),
    getPlayerStats(user.id)
  ]);

  // Get user's display name, avatar, and customization data
  const displayName = playerData?.display_name || user.user_metadata?.full_name || 'Anonymous Player';
  const currentAvatar = playerData?.avatar_url || user.user_metadata?.avatar_url;
  const currentTitle = playerData?.title || '';
  const currentBackground = playerData?.background_display || 'bg-gradient-to-r from-amber-100 to-amber-50';
  const currentBorder = playerData?.avatar_border || 'border-2 border-white';
  const currentRP = playerData?.rankpoints || 0;

  // Get rank information from RP
  const rankInfo = getRankInfo(currentRP);
  const userLevel = rankInfo.tierOrder; // Use tier order for compatibility with existing code
  const joinDate = new Date(user.created_at).toLocaleDateString('en-US', {
    month: 'long',
    year: 'numeric'
  });

  // Generate title based on stats
  const generateTitle = () => {
    if (playerStats.total_matches === 0) return "New Player";
    if (playerStats.win_rate >= 80) return "Champion ⭐ | Undefeated";
    if (playerStats.win_rate >= 60) return "Spell Master ⭐";
    if (playerStats.win_rate >= 40) return "Word Warrior";
    if (playerStats.total_matches >= 50) return "Veteran Player";
    return "Rising Speller";
  };

  // Badge system
  const badges = [
    {
      name: "First Victory",
      icon: Trophy,
      unlocked: playerStats.wins >= 1,
      description: "Win your first match",
      rarity: "common"
    },
    {
      name: "Speed Demon",
      icon: Zap,
      unlocked: playerStats.total_matches >= 10,
      description: "Play 10 matches",
      rarity: "common"
    },
    {
      name: "Word Wizard",
      icon: BookOpen,
      unlocked: playerStats.highest_score >= 1000,
      description: "Score over 1000 points",
      rarity: "rare"
    },
    {
      name: "Streak Master",
      icon: Flame,
      unlocked: playerStats.longest_streak >= 10,
      description: "Achieve a 10-word streak",
      rarity: "rare"
    },
    {
      name: "Champion",
      icon: Crown,
      unlocked: playerStats.wins >= 10,
      description: "Win 10 matches",
      rarity: "epic"
    },
    {
      name: "Perfect Game",
      icon: Star,
      unlocked: playerStats.win_rate >= 90 && playerStats.total_matches >= 10,
      description: "Maintain 90%+ win rate",
      rarity: "legendary"
    }
  ];

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-6 md:gap-8">
          {/* Header Section */}
          <header className="flex flex-col gap-2 sm:gap-4">
            <h1 className="text-2xl sm:text-3xl font-bold text-amber-900">My Profile</h1>
            <div className="bg-amber-100 text-xs sm:text-sm p-2 sm:p-3 px-3 sm:px-4 rounded-lg text-amber-800 flex gap-2 items-center border border-amber-200">
              <BookOpen size={14} className="hidden xs:inline" />
              <BookOpen size={12} className="xs:hidden" />
              <span>View and customize your Word Nook profile</span>
            </div>
          </header>

          {/* Profile Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Profile Card */}
            <div className="lg:col-span-1">
              <Card className="bg-white border overflow-hidden">
                <CardHeader className={cn("pb-4 sm:pb-6 relative", currentBackground)}>
                  <div className="absolute top-2 sm:top-4 right-2 sm:right-4">
                    <ProfileEditPopup
                      currentDisplayName={displayName}
                      currentTitle={currentTitle || generateTitle()}
                      currentBackground={currentBackground}
                      userLevel={userLevel}
                      userId={user.id}
                    >
                      <Button variant="ghost" size="icon" className="h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-white/80 hover:bg-white">
                        <Edit size={14} className="text-amber-800 sm:hidden" />
                        <Edit size={16} className="text-amber-800 hidden sm:inline" />
                      </Button>
                    </ProfileEditPopup>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className={cn(
                      "p-1 rounded-full",
                      currentBorder?.includes('bg-gradient') ? currentBorder : ''
                    )}>
                      <Avatar className={cn(
                        "h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 shadow-md",
                        !currentBorder?.includes('bg-gradient') ? currentBorder : 'border-2 border-white'
                      )}>
                        <AvatarImage src={currentAvatar} alt={displayName} />
                        <AvatarFallback className="bg-amber-200 text-amber-800 text-sm sm:text-base md:text-xl">
                          {displayName.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    <CardTitle className="mt-3 sm:mt-4 text-xl sm:text-2xl font-bold text-amber-900">
                      {displayName}
                    </CardTitle>
                    <CardDescription className="text-amber-700 font-medium text-sm sm:text-base text-center px-2">
                      {currentTitle || generateTitle()}
                    </CardDescription>
                    <div className="mt-2">
                      <RankDisplay
                        rp={currentRP}
                        variant="compact"
                        showProgress={false}
                        showRP={true}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-4 sm:pt-6 px-3 sm:px-6">
                  <div className="flex items-center gap-2 mb-4 text-amber-800">
                    <Calendar size={14} className="sm:hidden" />
                    <Calendar size={16} className="hidden sm:inline" />
                    <span className="text-xs sm:text-sm">Member since {joinDate}</span>
                  </div>

                  <div className="space-y-3 sm:space-y-4 mt-4 sm:mt-6">
                    <h3 className="font-semibold text-amber-900 flex items-center gap-2 text-sm sm:text-base">
                      <BarChart4 size={16} className="text-amber-700 sm:hidden" />
                      <BarChart4 size={18} className="text-amber-700 hidden sm:inline" />
                      Stats Overview
                    </h3>

                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Games Played</span>
                          <span className="font-medium text-amber-900">{playerStats.total_matches}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-amber-500" style={{ width: '100%' }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Wins ({playerStats.win_rate}% win rate)</span>
                          <span className="font-medium text-amber-900">{playerStats.wins}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-green-500" style={{ width: `${playerStats.win_rate}%` }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Highest Score</span>
                          <span className="font-medium text-amber-900">{playerStats.highest_score.toLocaleString()}</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-blue-500" style={{ width: `${Math.min(100, playerStats.highest_score / 100)}%` }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Longest Streak</span>
                          <span className="font-medium text-amber-900">{playerStats.longest_streak} words</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-red-500" style={{ width: `${Math.min(100, playerStats.longest_streak * 5)}%` }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-amber-800">Survival Rate</span>
                          <span className="font-medium text-amber-900">{playerStats.survival_rate}%</span>
                        </div>
                        <div className="h-2 bg-amber-100 rounded-full overflow-hidden">
                          <div className="h-full bg-purple-500" style={{ width: `${playerStats.survival_rate}%` }}></div>
                        </div>
                      </div>
                    </div>

                    {/* Rank Progress Section */}
                    <div className="mt-6 pt-4 border-t border-amber-200">
                      <RankDisplay
                        rp={currentRP}
                        variant="detailed"
                        showProgress={true}
                        showRP={true}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Tabs for different sections */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="badges" className="w-full">
                <TabsList className="grid grid-cols-3 sm:mb-6 bg-amber-100/50 p-1 sm:p-2 rounded-lg h-auto">
                  <TabsTrigger value="badges" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Award size={14} className="mr-1 sm:mr-2" />
                    <span>Badges</span>
                  </TabsTrigger>
                  <TabsTrigger value="history" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <History size={14} className="mr-1 sm:mr-2" />
                    <span>Match History</span>
                  </TabsTrigger>
                  <TabsTrigger value="settings" className="data-[state=active]:bg-amber-600 text-xs sm:text-sm py-1.5 sm:py-2">
                    <Settings size={14} className="mr-1 sm:mr-2" />
                    <span>Settings</span>
                  </TabsTrigger>
                </TabsList>

                {/* Badges & Achievements Tab */}
                <TabsContent value="badges" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <Award className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Badges & Achievements
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Unlock badges by completing special challenges
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4 mt-2 sm:mt-4">
                        {badges.map((badge, index) => {
                          const getRarityColor = (rarity: string) => {
                            switch (rarity) {
                              case 'common': return 'border-gray-300 bg-gray-50';
                              case 'rare': return 'border-blue-300 bg-blue-50';
                              case 'epic': return 'border-purple-300 bg-purple-50';
                              case 'legendary': return 'border-yellow-300 bg-yellow-50';
                              default: return 'border-gray-300 bg-gray-50';
                            }
                          };

                          return (
                            <div
                              key={index}
                              className={cn(
                                "flex flex-col items-center p-2 sm:p-4 rounded-lg border text-center transition-all duration-200",
                                badge.unlocked
                                  ? `${getRarityColor(badge.rarity)} hover:shadow-md`
                                  : "bg-gray-100 border-gray-200 opacity-70"
                              )}
                            >
                              <div className={cn(
                                "p-2 sm:p-3 rounded-full mb-1 sm:mb-2 relative",
                                badge.unlocked ? "bg-amber-200" : "bg-gray-200"
                              )}>
                                <badge.icon
                                  size={16}
                                  className={cn(
                                    "sm:hidden",
                                    badge.unlocked ? "text-amber-700" : "text-gray-500"
                                  )}
                                />
                                <badge.icon
                                  size={24}
                                  className={cn(
                                    "hidden sm:block",
                                    badge.unlocked ? "text-amber-700" : "text-gray-500"
                                  )}
                                />
                                {!badge.unlocked && (
                                  <Lock size={12} className="absolute -top-1 -right-1 text-gray-400" />
                                )}
                                {badge.unlocked && (
                                  <Check size={12} className="absolute -top-1 -right-1 text-green-500 bg-white rounded-full" />
                                )}
                              </div>
                              <h3 className={cn(
                                "font-medium mb-0.5 sm:mb-1 text-xs sm:text-sm",
                                badge.unlocked ? "text-amber-900" : "text-gray-600"
                              )}>
                                {badge.name}
                              </h3>
                              <Badge
                                variant="outline"
                                className={cn(
                                  "text-[10px] mb-1",
                                  badge.rarity === 'common' && "border-gray-300 text-gray-600",
                                  badge.rarity === 'rare' && "border-blue-300 text-blue-600",
                                  badge.rarity === 'epic' && "border-purple-300 text-purple-600",
                                  badge.rarity === 'legendary' && "border-yellow-300 text-yellow-600"
                                )}
                              >
                                {badge.rarity}
                              </Badge>
                              <p className="text-[10px] sm:text-xs text-gray-600 line-clamp-2">
                                {badge.description}
                              </p>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Match History Tab */}
                <TabsContent value="history" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <History className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Match History
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Your recent matches and performance details
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      {matchHistory.length === 0 ? (
                        <div className="text-center py-12">
                          <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No Match History</h3>
                          <p className="text-gray-500">Start playing to see your match history here!</p>
                        </div>
                      ) : (
                        <div className="space-y-3 mt-4">
                          {matchHistory.map((match, index) => (
                            <Dialog key={match.match_id}>
                              <DialogTrigger asChild>
                                <div className={cn(
                                  "p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-gray-50",
                                  index === 0 ? "ring-2 ring-amber-200 bg-amber-50/50" : "bg-white border-gray-200"
                                )}>
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                      <div className="flex items-center gap-2">
                                        {match.final_rank === 1 && <Trophy className="h-5 w-5 text-yellow-500" />}
                                        {match.final_rank === 2 && <Medal className="h-5 w-5 text-gray-400" />}
                                        {match.final_rank === 3 && <Medal className="h-5 w-5 text-amber-600" />}
                                        {match.final_rank > 3 && <Target className="h-5 w-5 text-gray-500" />}
                                        <span className="font-semibold text-lg">
                                          {getRankDisplay(match.final_rank)}
                                        </span>
                                      </div>

                                      <div className="flex flex-col">
                                        <div className="flex items-center gap-2 mb-1">
                                          <Badge className={cn("text-xs font-medium capitalize", getDifficultyColor(match.difficulty))}>
                                            {match.difficulty}
                                          </Badge>
                                          <span className="text-sm text-gray-500">
                                            {match.total_players} players
                                          </span>
                                        </div>

                                        <div className="flex items-center gap-4 text-sm text-gray-600">
                                          <div className="flex items-center gap-1">
                                            <TrendingUp className="h-3 w-3" />
                                            <span>{match.score.toLocaleString()} pts</span>
                                          </div>
                                          <div className="flex items-center gap-1">
                                            <Clock className="h-3 w-3" />
                                            <span>{formatMatchDuration(match.match_duration_seconds)}</span>
                                          </div>
                                          {match.elimination_round && (
                                            <div className="flex items-center gap-1">
                                              <Flame className="h-3 w-3 text-red-500" />
                                              <span>Round {match.elimination_round}</span>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>

                                    <div className="text-right">
                                      <Badge variant="outline" className="text-xs mb-1">
                                        {new Date(match.ended_at).toLocaleDateString()}
                                      </Badge>
                                      {index === 0 && (
                                        <Badge className="text-xs bg-amber-100 text-amber-800 border-amber-200 block">
                                          Latest
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </DialogTrigger>

                              <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                  <DialogTitle className="flex items-center gap-2">
                                    <Trophy className="h-5 w-5 text-amber-600" />
                                    Match Details - {getRankDisplay(match.final_rank)}
                                  </DialogTitle>
                                </DialogHeader>

                                <div className="space-y-6">
                                  {/* Match Overview */}
                                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                      <p className="text-2xl font-bold text-amber-700">{match.score.toLocaleString()}</p>
                                      <p className="text-sm text-gray-600">Final Score</p>
                                    </div>
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                      <p className="text-2xl font-bold text-blue-700">{match.final_rank}</p>
                                      <p className="text-sm text-gray-600">Final Rank</p>
                                    </div>
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                      <p className="text-2xl font-bold text-green-700">{match.current_round}</p>
                                      <p className="text-sm text-gray-600">Rounds Survived</p>
                                    </div>
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                      <p className="text-2xl font-bold text-purple-700">{match.total_players}</p>
                                      <p className="text-sm text-gray-600">Total Players</p>
                                    </div>
                                  </div>

                                  {/* Match Info */}
                                  <div className="space-y-3">
                                    <h3 className="font-semibold text-gray-900">Match Information</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">Difficulty:</span>
                                        <Badge className={cn("capitalize", getDifficultyColor(match.difficulty))}>
                                          {match.difficulty}
                                        </Badge>
                                      </div>
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">Duration:</span>
                                        <span className="font-medium">{formatMatchDuration(match.match_duration_seconds)}</span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">Date:</span>
                                        <span className="font-medium">{new Date(match.ended_at).toLocaleDateString()}</span>
                                      </div>
                                      {match.elimination_round && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-600">Eliminated in:</span>
                                          <span className="font-medium text-red-600">Round {match.elimination_round}</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Players in Match */}
                                  {match.player_names.length > 0 && (
                                    <div className="space-y-3">
                                      <h3 className="font-semibold text-gray-900">Players in this Match</h3>
                                      <div className="flex flex-wrap gap-2">
                                        {match.player_names.map((playerName, idx) => (
                                          <Badge key={idx} variant="outline" className="text-xs">
                                            {playerName}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </DialogContent>
                            </Dialog>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="mt-0">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-2 px-3 sm:px-6">
                      <CardTitle className="text-lg sm:text-xl text-amber-900 flex items-center gap-2">
                        <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                        Settings & Customization
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Personalize your Word Nook experience
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6">
                      <div className="space-y-4 sm:space-y-6 mt-2 sm:mt-4">
                        {/* Avatar Selection */}
                        <AvatarSelector
                          currentAvatar={currentAvatar}
                          userLevel={userLevel}
                          userId={user.id}
                        />

                        {/* Border Selection */}
                        <BorderSelector
                          currentAvatar={currentAvatar}
                          currentBorder={currentBorder}
                          userLevel={userLevel}
                          userId={user.id}
                          displayName={displayName}
                        />

                        {/* Privacy Settings */}
                        <PrivacySettings userId={user.id} />

                        {/* Audio Settings */}
                        <div>
                          <h3 className="font-medium text-amber-900 mb-2 sm:mb-3 text-sm sm:text-base">Audio Settings</h3>
                          <div className="space-y-2 sm:space-y-3">
                            <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                              <div className="flex items-center gap-2 sm:gap-3">
                                <Volume2 size={14} className="text-amber-700 sm:hidden" />
                                <Volume2 size={18} className="text-amber-700 hidden sm:block" />
                                <span className="text-amber-900 text-xs sm:text-sm">Word Pronunciations</span>
                              </div>
                              <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-amber-600">
                                <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-[18px] sm:translate-x-6"></span>
                              </div>
                            </div>

                            <div className="flex items-center justify-between p-2 sm:p-3 rounded-lg border border-amber-100 bg-white">
                              <div className="flex items-center gap-2 sm:gap-3">
                                <VolumeX size={14} className="text-amber-700 sm:hidden" />
                                <VolumeX size={18} className="text-amber-700 hidden sm:block" />
                                <span className="text-amber-900 text-xs sm:text-sm">Background Music</span>
                              </div>
                              <div className="relative inline-flex h-5 w-9 sm:h-6 sm:w-11 items-center rounded-full bg-gray-300">
                                <span className="absolute h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-white transition-transform translate-x-1"></span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end pt-0 px-3 sm:px-6 pb-3 sm:pb-4">
                      <Button className="bg-amber-600 hover:bg-amber-700 text-white text-xs sm:text-sm h-8 sm:h-10">
                        Save Changes
                      </Button>
                    </CardFooter>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};
