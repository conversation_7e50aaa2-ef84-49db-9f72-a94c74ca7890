"use client";

import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { getRankInfo, getRankDisplayString, getRankColorClasses, formatRP, RankInfo } from "@/utils/ranking-system";
import { Crown, Star, Award, Shield, Scroll } from "lucide-react";

interface RankDisplayProps {
  rp: number;
  variant?: 'compact' | 'detailed' | 'card' | 'inline';
  showProgress?: boolean;
  showRP?: boolean;
  className?: string;
}

interface RankBadgeProps {
  tier: string;
  division?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface RankProgressProps {
  rankInfo: RankInfo;
  className?: string;
}

// Get rank icon based on tier
const getRankIcon = (tier: string, size: 'sm' | 'md' | 'lg' = 'md') => {
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const iconClass = sizeClasses[size];

  switch (tier) {
    case 'Luminari':
      return <Crown className={cn(iconClass, "text-yellow-500")} />;
    case 'Sage':
      return <Award className={cn(iconClass, "text-purple-500")} />;
    case 'Lexicon':
      return <Shield className={cn(iconClass, "text-blue-500")} />;
    case 'Quill':
      return <Star className={cn(iconClass, "text-green-500")} />;
    case 'Parchment':
    default:
      return <Scroll className={cn(iconClass, "text-gray-500")} />;
  }
};

// Rank Badge Component
export function RankBadge({ tier, division, size = 'md', className }: RankBadgeProps) {
  const colors = getRankColorClasses(tier);
  
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  return (
    <Badge 
      className={cn(
        `bg-gradient-to-r ${colors.gradient} text-white border-0 font-semibold`,
        sizeClasses[size],
        className
      )}
    >
      <div className="flex items-center gap-1">
        {getRankIcon(tier, size)}
        <span>
          {tier}
          {division && tier !== 'Luminari' && ` ${division}`}
        </span>
      </div>
    </Badge>
  );
}

// Rank Progress Component
export function RankProgress({ rankInfo, className }: RankProgressProps) {
  if (rankInfo.tier === 'Luminari') {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-yellow-700">Luminari</span>
          <span className="text-sm text-yellow-600">{formatRP(rankInfo.rp)}</span>
        </div>
        <div className="w-full bg-gradient-to-r from-yellow-200 to-orange-200 rounded-full h-2">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full w-full"></div>
        </div>
        <p className="text-xs text-center text-yellow-600">Maximum Rank Achieved</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium">{getRankDisplayString(rankInfo)}</span>
        <span className="text-sm text-gray-600">{formatRP(rankInfo.rp)}</span>
      </div>
      <Progress 
        value={rankInfo.progressPercentage} 
        className="h-2"
      />
      <div className="flex justify-between text-xs text-gray-500">
        <span>{formatRP(rankInfo.rpForCurrentRank)}</span>
        <span>{formatRP(rankInfo.rpToNextRank)} to next</span>
        <span>{formatRP(rankInfo.rpForNextRank)}</span>
      </div>
    </div>
  );
}

// Main Rank Display Component
export default function RankDisplay({ 
  rp, 
  variant = 'compact', 
  showProgress = true, 
  showRP = true,
  className 
}: RankDisplayProps) {
  const rankInfo = getRankInfo(rp);
  const colors = getRankColorClasses(rankInfo.tier);

  if (variant === 'inline') {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        {getRankIcon(rankInfo.tier, 'sm')}
        <span className={cn("text-sm font-medium", colors.text)}>
          {getRankDisplayString(rankInfo)}
        </span>
        {showRP && (
          <span className="text-xs text-gray-500">
            ({formatRP(rp)})
          </span>
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <RankBadge tier={rankInfo.tier} division={rankInfo.division} size="sm" />
        {showRP && (
          <span className="text-sm text-gray-600">{formatRP(rp)}</span>
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={cn("space-y-3", className)}>
        <div className="flex items-center justify-between">
          <RankBadge tier={rankInfo.tier} division={rankInfo.division} size="md" />
          {showRP && (
            <span className="text-lg font-semibold text-gray-800">{formatRP(rp)}</span>
          )}
        </div>
        {showProgress && <RankProgress rankInfo={rankInfo} />}
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <div className={cn(
        "bg-gradient-to-br from-white to-gray-50 rounded-lg p-4 border shadow-sm",
        className
      )}>
        <div className="flex items-center justify-between mb-3">
          <RankBadge tier={rankInfo.tier} division={rankInfo.division} size="lg" />
          {showRP && (
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-800">{formatRP(rp)}</div>
              <div className="text-xs text-gray-500">Rank Points</div>
            </div>
          )}
        </div>
        {showProgress && <RankProgress rankInfo={rankInfo} />}
      </div>
    );
  }

  return null;
}

// Rank Comparison Component (for showing rank changes)
interface RankComparisonProps {
  oldRP: number;
  newRP: number;
  className?: string;
}

export function RankComparison({ oldRP, newRP, className }: RankComparisonProps) {
  const oldRankInfo = getRankInfo(oldRP);
  const newRankInfo = getRankInfo(newRP);
  
  const rankUp = newRankInfo.tierOrder > oldRankInfo.tierOrder || 
                (newRankInfo.tierOrder === oldRankInfo.tierOrder && newRankInfo.divisionOrder > oldRankInfo.divisionOrder);
  const rankDown = newRankInfo.tierOrder < oldRankInfo.tierOrder || 
                  (newRankInfo.tierOrder === oldRankInfo.tierOrder && newRankInfo.divisionOrder < oldRankInfo.divisionOrder);

  return (
    <div className={cn("flex items-center gap-4", className)}>
      <div className="text-center">
        <div className="text-xs text-gray-500 mb-1">Previous</div>
        <RankBadge tier={oldRankInfo.tier} division={oldRankInfo.division} size="sm" />
        <div className="text-xs text-gray-600 mt-1">{formatRP(oldRP)}</div>
      </div>
      
      <div className="flex flex-col items-center">
        {rankUp ? (
          <div className="text-green-600 text-xs font-medium">RANK UP!</div>
        ) : rankDown ? (
          <div className="text-red-600 text-xs font-medium">RANK DOWN</div>
        ) : (
          <div className="text-gray-500 text-xs">→</div>
        )}
      </div>
      
      <div className="text-center">
        <div className="text-xs text-gray-500 mb-1">Current</div>
        <RankBadge tier={newRankInfo.tier} division={newRankInfo.division} size="sm" />
        <div className="text-xs text-gray-600 mt-1">{formatRP(newRP)}</div>
      </div>
    </div>
  );
}

// Leaderboard Rank Display (for showing rank in lists)
interface LeaderboardRankProps {
  rp: number;
  position: number;
  playerName: string;
  avatar?: string;
  className?: string;
}

export function LeaderboardRank({ rp, position, playerName, className }: LeaderboardRankProps) {
  const rankInfo = getRankInfo(rp);
  
  const getPositionStyling = (pos: number) => {
    if (pos === 1) return "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white";
    if (pos === 2) return "bg-gradient-to-r from-gray-300 to-gray-400 text-white";
    if (pos === 3) return "bg-gradient-to-r from-amber-600 to-amber-700 text-white";
    return "bg-gray-100 text-gray-700";
  };

  return (
    <div className={cn("flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50", className)}>
      <div className={cn(
        "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
        getPositionStyling(position)
      )}>
        {position}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="font-medium text-gray-900 truncate">{playerName}</div>
        <RankDisplay rp={rp} variant="inline" showRP={false} />
      </div>
      
      <div className="text-right">
        <div className="font-semibold text-gray-800">{formatRP(rp)}</div>
        <div className="text-xs text-gray-500">RP</div>
      </div>
    </div>
  );
}
