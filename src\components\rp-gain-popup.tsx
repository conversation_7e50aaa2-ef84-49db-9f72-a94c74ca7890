"use client";

import { useEffect, useState } from "react";
import { createClient } from "../../supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Trophy, TrendingUp, TrendingDown, Crown, Star, ArrowUp, ArrowDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { getRankInfo, calculateRPChange, getRankDisplayString, getRankColorClasses, formatRP } from "@/utils/ranking-system";

interface RPGainPopupProps {
  matchId: string;
  finalRank: number;
  totalPlayers: number;
  score: number;
  difficulty: string;
  eliminationRound?: number;
  isVisible: boolean;
  onClose: () => void;
}

interface RPGainData {
  rpChange: number;
  oldRP: number;
  newRP: number;
  oldRank: string;
  newRank: string;
  breakdown: string[];
  rankUp: boolean;
  rankDown: boolean;
}

export default function RPGainPopup({
  matchId,
  finalRank,
  totalPlayers,
  score,
  difficulty,
  eliminationRound,
  isVisible,
  onClose
}: RPGainPopupProps) {
  const [rpGainData, setRPGainData] = useState<RPGainData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isVisible || !matchId) return;

    const fetchRPGainData = async () => {
      try {
        setLoading(true);
        const supabase = createClient();

        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setError("User not authenticated");
          return;
        }

        // Get match history data for this specific match and player
        const { data: matchHistoryData, error: historyError } = await supabase
          .from('match_history_players')
          .select('rp_gained, rp_before_match, rp_after_match')
          .eq('match_id', matchId)
          .eq('player_id', user.id)
          .single();

        if (historyError || !matchHistoryData) {
          console.error('Error fetching match history:', historyError);
          
          // Fallback: calculate RP change manually
          const { data: playerData } = await supabase
            .from('players')
            .select('rankpoints')
            .eq('id', user.id)
            .single();

          const currentRP = playerData?.rankpoints || 0;
          
          // Get match duration for calculation
          const { data: matchData } = await supabase
            .from('match_histories')
            .select('match_duration_seconds')
            .eq('id', matchId)
            .single();

          const durationMinutes = (matchData?.match_duration_seconds || 300) / 60;
          
          // Calculate average score (simplified)
          const averageScore = score * 0.8; // Rough estimate
          
          const rpChange = calculateRPChange(
            currentRP,
            finalRank,
            totalPlayers,
            durationMinutes,
            difficulty,
            score,
            averageScore
          );

          const oldRankInfo = getRankInfo(currentRP);
          const newRankInfo = getRankInfo(Math.max(0, currentRP + rpChange.totalRP));

          setRPGainData({
            rpChange: rpChange.totalRP,
            oldRP: currentRP,
            newRP: Math.max(0, currentRP + rpChange.totalRP),
            oldRank: getRankDisplayString(oldRankInfo),
            newRank: getRankDisplayString(newRankInfo),
            breakdown: rpChange.breakdown,
            rankUp: newRankInfo.tierOrder > oldRankInfo.tierOrder || 
                   (newRankInfo.tierOrder === oldRankInfo.tierOrder && newRankInfo.divisionOrder > oldRankInfo.divisionOrder),
            rankDown: newRankInfo.tierOrder < oldRankInfo.tierOrder || 
                     (newRankInfo.tierOrder === oldRankInfo.tierOrder && newRankInfo.divisionOrder < oldRankInfo.divisionOrder)
          });
        } else {
          // Use actual data from database
          const oldRankInfo = getRankInfo(matchHistoryData.rp_before_match);
          const newRankInfo = getRankInfo(matchHistoryData.rp_after_match);

          setRPGainData({
            rpChange: matchHistoryData.rp_gained,
            oldRP: matchHistoryData.rp_before_match,
            newRP: matchHistoryData.rp_after_match,
            oldRank: getRankDisplayString(oldRankInfo),
            newRank: getRankDisplayString(newRankInfo),
            breakdown: [], // Could be stored in database if needed
            rankUp: newRankInfo.tierOrder > oldRankInfo.tierOrder || 
                   (newRankInfo.tierOrder === oldRankInfo.tierOrder && newRankInfo.divisionOrder > oldRankInfo.divisionOrder),
            rankDown: newRankInfo.tierOrder < oldRankInfo.tierOrder || 
                     (newRankInfo.tierOrder === oldRankInfo.tierOrder && newRankInfo.divisionOrder < oldRankInfo.divisionOrder)
          });
        }

      } catch (err) {
        console.error('Error fetching RP gain data:', err);
        setError("Failed to load RP data");
      } finally {
        setLoading(false);
      }
    };

    fetchRPGainData();
  }, [isVisible, matchId, finalRank, totalPlayers, score, difficulty]);

  if (!isVisible) return null;

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-6 w-6 text-yellow-500" />;
    if (rank <= 3) return <Trophy className="h-6 w-6 text-amber-500" />;
    return <Star className="h-6 w-6 text-gray-500" />;
  };

  const getPerformanceColor = (rank: number, total: number) => {
    const percentile = (total - rank + 1) / total;
    if (percentile >= 0.8) return "text-green-600 bg-green-50";
    if (percentile >= 0.6) return "text-blue-600 bg-blue-50";
    if (percentile >= 0.4) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md mx-auto shadow-2xl border-2">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center gap-2 mb-2">
            {getRankIcon(finalRank)}
            <CardTitle className="text-2xl">Match Complete!</CardTitle>
          </div>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="outline" className={cn("text-sm", getPerformanceColor(finalRank, totalPlayers))}>
              Rank #{finalRank} of {totalPlayers}
            </Badge>
            <Badge variant="outline" className="capitalize">
              {difficulty}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
              <p className="text-sm text-gray-600 mt-2">Calculating RP...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
            </div>
          ) : rpGainData ? (
            <>
              {/* RP Change Display */}
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  {rpGainData.rpChange > 0 ? (
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  ) : (
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  )}
                  <span className={cn(
                    "text-3xl font-bold",
                    rpGainData.rpChange > 0 ? "text-green-600" : "text-red-600"
                  )}>
                    {rpGainData.rpChange > 0 ? '+' : ''}{rpGainData.rpChange} RP
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {formatRP(rpGainData.oldRP)} → {formatRP(rpGainData.newRP)}
                </p>
              </div>

              {/* Rank Change */}
              <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="text-center flex-1">
                    <p className="text-xs text-gray-600 mb-1">Previous Rank</p>
                    <p className="font-semibold text-gray-800">{rpGainData.oldRank}</p>
                  </div>
                  
                  <div className="flex items-center px-4">
                    {rpGainData.rankUp ? (
                      <ArrowUp className="h-6 w-6 text-green-600" />
                    ) : rpGainData.rankDown ? (
                      <ArrowDown className="h-6 w-6 text-red-600" />
                    ) : (
                      <div className="h-6 w-6 rounded-full bg-gray-300"></div>
                    )}
                  </div>
                  
                  <div className="text-center flex-1">
                    <p className="text-xs text-gray-600 mb-1">Current Rank</p>
                    <p className="font-semibold text-gray-800">{rpGainData.newRank}</p>
                  </div>
                </div>

                {(rpGainData.rankUp || rpGainData.rankDown) && (
                  <div className="text-center mt-3">
                    <Badge variant={rpGainData.rankUp ? "default" : "destructive"} className="text-xs">
                      {rpGainData.rankUp ? "Rank Up!" : "Rank Down"}
                    </Badge>
                  </div>
                )}
              </div>

              {/* Match Stats */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <p className="text-gray-600">Final Score</p>
                  <p className="font-semibold text-lg">{score.toLocaleString()}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600">Placement</p>
                  <p className="font-semibold text-lg">#{finalRank}</p>
                </div>
              </div>

              {/* Close Button */}
              <Button onClick={onClose} className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700">
                Continue
              </Button>
            </>
          ) : null}
        </CardContent>
      </Card>
    </div>
  );
}
